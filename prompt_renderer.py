"""
Prompt模板渲染器 - 使用Jinja2处理动态prompt模板
"""

from typing import Optional, Dict, Any
from pathlib import Path
import jinja2
from jinja2 import Environment, FileSystemLoader, Template

from config import config


class PromptRenderer:
    """
    Prompt模板渲染器

    负责读取和渲染prompt模板文件，支持动态数据注入
    """

    def __init__(self):
        """初始化渲染器"""
        # 创建Jinja2环境
        self.env = Environment(
            loader=FileSystemLoader(config.PROMPT_DIR),
            trim_blocks=True,
            lstrip_blocks=True,
            keep_trailing_newline=True,
        )

        # 缓存模板
        self._template_cache: Dict[str, Template] = {}

    def _load_template(self, template_name: str) -> Template:
        """加载模板文件"""
        if template_name not in self._template_cache:
            try:
                self._template_cache[template_name] = self.env.get_template(
                    template_name
                )
            except jinja2.TemplateNotFound:
                raise FileNotFoundError(f"Template file not found: {template_name}")
            except jinja2.TemplateSyntaxError as e:
                raise ValueError(f"Template syntax error in {template_name}: {e}")

        return self._template_cache[template_name]

    def render_parse_pdf_prompt(self) -> str:
        """
        渲染PDF解析prompt

        PARSER_PDF.md不需要动态数据注入，直接读取内容
        """
        try:
            template = self._load_template("PARSER_PDF.md")
            return template.render()
        except Exception as e:
            raise RuntimeError(f"Failed to render PARSER_PDF prompt: {e}")

    def render_table_judge_prompt(
        self, last_page_table_last_row: Optional[str] = None
    ) -> str:
        """
        渲染表格判断prompt

        Args:
            last_page_table_last_row: 上一页表格的最后一行数据

        Returns:
            渲染后的prompt内容
        """
        try:
            template = self._load_template("TABLE_JUDGE.md")

            # 准备模板变量
            template_vars = {
                "last_page_table_last_row": last_page_table_last_row
                or "无上一页表格数据"
            }

            # 渲染模板
            rendered_content = template.render(**template_vars)

            return rendered_content

        except Exception as e:
            raise RuntimeError(f"Failed to render TABLE_JUDGE prompt: {e}")

    def validate_template(self, template_name: str) -> bool:
        """
        验证模板文件的语法

        Args:
            template_name: 模板文件名

        Returns:
            是否验证通过
        """
        try:
            self._load_template(template_name)
            return True
        except Exception:
            return False

    def get_template_variables(self, template_name: str) -> list:
        """
        获取模板中使用的变量列表

        Args:
            template_name: 模板文件名

        Returns:
            变量名列表
        """
        try:
            template = self._load_template(template_name)
            # 获取模板中的未定义变量
            ast = self.env.parse(template.source)
            variables = jinja2.meta.find_undeclared_variables(ast)
            return list(variables)
        except Exception as e:
            raise RuntimeError(f"Failed to analyze template variables: {e}")

    def render_with_custom_data(self, template_name: str, **kwargs) -> str:
        """
        使用自定义数据渲染模板

        Args:
            template_name: 模板文件名
            **kwargs: 模板变量

        Returns:
            渲染后的内容
        """
        try:
            template = self._load_template(template_name)
            return template.render(**kwargs)
        except Exception as e:
            raise RuntimeError(f"Failed to render template {template_name}: {e}")


class PromptValidator:
    """Prompt验证器 - 验证prompt内容的完整性"""

    @staticmethod
    def validate_parse_pdf_prompt(content: str) -> bool:
        """验证PDF解析prompt的内容"""
        required_sections = [
            "黄金三大原则",
            "绝对忠实原则",
            "清晰边界原则",
            "结构优先原则",
            "四步表格解析法",
            "返回格式",
        ]

        for section in required_sections:
            if section not in content:
                return False

        return True

    @staticmethod
    def validate_table_judge_prompt(content: str) -> bool:
        """验证表格判断prompt的内容"""
        required_sections = [
            "角色定义",
            "所需信息",
            "判定逻辑",
            "阶段一：视觉判断",
            "阶段二：数据行完整性判断",
            "返回格式",
        ]

        for section in required_sections:
            if section not in content:
                print(f"Missing section: {section}")  # 调试信息
                return False

        # 检查是否包含模板变量占位符
        if "{{last_page_table_last_row}}" not in content:
            print("Missing template variable: {{last_page_table_last_row}}")  # 调试信息
            return False

        return True

    @staticmethod
    def validate_judgment_result(result: str) -> bool:
        """验证判断结果的格式"""
        valid_results = ["新表格", "续表，开始新行", "续表，合并首行"]

        # 检查是否包含"判定结果:"
        if "判定结果:" not in result:
            return False

        # 提取结果值
        try:
            result_value = result.split("判定结果:")[1].strip()
            return result_value in valid_results
        except (IndexError, AttributeError):
            return False


# 全局渲染器实例
prompt_renderer = PromptRenderer()

# 在模块加载时验证模板 - 暂时禁用以避免阻止程序启动
if __name__ == "__main__":  # 只在直接运行时验证
    try:
        # 验证模板语法
        assert prompt_renderer.validate_template(
            "PARSER_PDF.md"
        ), "PARSER_PDF.md template validation failed"
        assert prompt_renderer.validate_template(
            "TABLE_JUDGE.md"
        ), "TABLE_JUDGE.md template validation failed"

        # 验证模板内容
        parse_pdf_content = prompt_renderer.render_parse_pdf_prompt()
        assert PromptValidator.validate_parse_pdf_prompt(
            parse_pdf_content
        ), "PARSER_PDF.md content validation failed"

        # 直接读取原始模板文件进行验证
        from config import config

        with open(config.TABLE_JUDGE_PROMPT_PATH, "r", encoding="utf-8") as f:
            table_judge_raw_content = f.read()
        assert PromptValidator.validate_table_judge_prompt(
            table_judge_raw_content
        ), "TABLE_JUDGE.md content validation failed"

    except Exception as e:
        print(f"Prompt template validation error: {e}")
        raise
