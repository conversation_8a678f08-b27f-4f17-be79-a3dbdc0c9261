"""
Agent配置 - 创建和配置Qwen-VL React Agents
"""

import logging
from typing import Optional, Dict, Any, List
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_openai import ChatOpenAI  # 使用OpenAI兼容接口
from langgraph.prebuilt import create_react_agent
from langchain_core.tools import Tool

from config import config
from prompt_renderer import prompt_renderer
from utils import RetryHelper


class QwenVLModel:
    """Qwen-VL模型封装器"""

    def __init__(self):
        """初始化Qwen-VL模型"""
        self.model = ChatOpenAI(
            api_key=config.QWEN_VL_API_KEY,
            base_url=config.QWEN_VL_BASE_URL,
            model=config.QWEN_VL_MODEL,
            temperature=config.MODEL_TEMPERATURE,
            max_tokens=config.MODEL_MAX_TOKENS,
            timeout=config.MODEL_TIMEOUT,
        )

    def get_model(self) -> BaseChatModel:
        """获取模型实例"""
        return self.model


class VisionTools:
    """视觉分析工具集"""

    @staticmethod
    def create_image_analysis_tool() -> Tool:
        """创建图像分析工具"""

        def analyze_image(image_base64: str, prompt: str) -> str:
            """分析图像内容"""
            try:
                # 这里可以添加具体的图像分析逻辑
                # 目前返回提示信息，实际使用时会通过VLM模型处理
                return f"Image analysis with prompt: {prompt[:100]}..."
            except Exception as e:
                logging.error(f"Image analysis failed: {e}")
                return f"Error: {e}"

        return Tool(
            name="image_analyzer",
            description="Analyze image content with given prompt",
            func=analyze_image,
        )


class PDFParserAgent:
    """PDF解析Agent"""

    def __init__(self):
        """初始化PDF解析Agent"""
        self.model = QwenVLModel().get_model()
        self.system_prompt = prompt_renderer.render_parse_pdf_prompt()

        # 创建工具集
        tools = [VisionTools.create_image_analysis_tool()]

        # 创建React Agent
        self.agent = create_react_agent(
            model=self.model, tools=tools, prompt=self.system_prompt
        )

    @RetryHelper.retry_on_failure
    def parse_page(self, image_base64: str) -> str:
        """
        解析PDF页面为HTML

        Args:
            image_base64: 页面图像的base64编码

        Returns:
            解析后的HTML内容
        """
        try:
            # 构建消息
            messages = [
                HumanMessage(
                    content=[
                        {
                            "type": "text",
                            "text": "请按照系统提示中的要求，将这个PDF页面图像转换为HTML格式。",
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            },
                        },
                    ]
                )
            ]

            logging.info(
                f"Sending PDF parsing request with image size: {len(image_base64)} chars"
            )

            # 调用agent，设置超时
            config = {"timeout": 600}  # 5分钟超时
            result = self.agent.invoke({"messages": messages}, config=config)

            # 提取HTML内容
            if "messages" in result and result["messages"]:
                last_message = result["messages"][-1]
                if hasattr(last_message, "content"):
                    return self._extract_html_from_response(last_message.content)

            raise ValueError("No valid response from PDF parser agent")

        except Exception as e:
            logging.error(f"PDF parsing failed: {e}")
            raise RuntimeError(f"PDF parsing error: {e}")

    def _extract_html_from_response(self, response: str) -> str:
        """从响应中提取HTML内容"""
        # 查找HTML标记
        if "HTML：" in response:
            html_start = response.find("HTML：") + len("HTML：")
            html_content = response[html_start:].strip()
        elif "HTML:" in response:
            html_start = response.find("HTML:") + len("HTML:")
            html_content = response[html_start:].strip()
        else:
            # 如果没有明确标记，尝试提取HTML标签内容
            html_content = response.strip()

        # 移除代码块包裹
        if html_content.startswith("```html"):
            html_content = html_content[7:]  # 移除开头的```html
        elif html_content.startswith("```"):
            html_content = html_content[3:]  # 移除开头的```

        if html_content.endswith("```"):
            html_content = html_content[:-3]  # 移除结尾的```

        return html_content.strip()


class TableJudgeAgent:
    """表格判断Agent"""

    def __init__(self):
        """初始化表格判断Agent"""
        self.model = QwenVLModel().get_model()

        # 创建工具集
        tools = [VisionTools.create_image_analysis_tool()]

        # 创建React Agent（系统提示会在调用时动态设置）
        self.agent = create_react_agent(model=self.model, tools=tools)

    @RetryHelper.retry_on_failure
    def judge_table_continuation(
        self, image_base64: str, last_page_table_last_row: Optional[str] = None
    ) -> str:
        """
        判断表格续表情况

        Args:
            image_base64: 页面图像的base64编码
            last_page_table_last_row: 上一页表格的最后一行数据

        Returns:
            判断结果："新表格" | "续表，开始新行" | "续表，合并首行"
        """
        try:
            # 渲染带有动态数据的prompt
            system_prompt = prompt_renderer.render_table_judge_prompt(
                last_page_table_last_row
            )

            # 构建消息
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(
                    content=[
                        {
                            "type": "text",
                            "text": "请按照系统提示中的要求，分析这个PDF页面图像，判断页面顶部的表格情况。",
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            },
                        },
                    ]
                ),
            ]

            logging.info(
                f"Sending table judgment request with image size: {len(image_base64)} chars, and last row: {last_page_table_last_row or 'None'}"
            )

            # 调用agent，设置超时
            config = {"timeout": 600}  # 5分钟超时
            result = self.agent.invoke({"messages": messages}, config=config)

            # 提取判断结果
            if "messages" in result and result["messages"]:
                last_message = result["messages"][-1]
                if hasattr(last_message, "content"):
                    return self._extract_judgment_from_response(last_message.content)

            raise ValueError("No valid response from table judge agent")

        except Exception as e:
            logging.error(f"Table judgment failed: {e}")
            raise RuntimeError(f"Table judgment error: {e}")

    def _extract_judgment_from_response(self, response: str) -> str:
        """从响应中提取判断结果"""
        # 查找判定结果
        if "判定结果:" in response:
            result_start = response.find("判定结果:") + len("判定结果:")
            result_line = response[result_start:].split("\n")[0].strip()
            return result_line
        elif "判定结果：" in response:
            result_start = response.find("判定结果：") + len("判定结果：")
            result_line = response[result_start:].split("\n")[0].strip()
            return result_line
        else:
            # 如果没有找到标准格式，尝试从响应中提取
            valid_results = ["新表格", "续表，开始新行", "续表，合并首行"]
            for result in valid_results:
                if result in response:
                    return result

            # 默认返回新表格
            logging.warning(
                f"Could not extract judgment result from response: {response[:200]}..."
            )
            return "新表格"


class AgentManager:
    """Agent管理器 - 统一管理所有Agent"""

    def __init__(self):
        """初始化Agent管理器"""
        self.pdf_parser = PDFParserAgent()
        self.table_judge = TableJudgeAgent()

        logging.info("All agents initialized successfully")

    def get_pdf_parser(self) -> PDFParserAgent:
        """获取PDF解析Agent"""
        return self.pdf_parser

    def get_table_judge(self) -> TableJudgeAgent:
        """获取表格判断Agent"""
        return self.table_judge

    def test_agents(self) -> Dict[str, bool]:
        """测试所有Agent的可用性"""
        results = {}

        try:
            # 测试PDF解析Agent
            _ = self.pdf_parser.model.invoke([HumanMessage(content="测试连接")])
            results["pdf_parser"] = True
            logging.info("PDF parser agent test passed")
        except Exception as e:
            results["pdf_parser"] = False
            logging.error(f"PDF parser agent test failed: {e}")

        try:
            # 测试表格判断Agent
            _ = self.table_judge.model.invoke([HumanMessage(content="测试连接")])
            results["table_judge"] = True
            logging.info("Table judge agent test passed")
        except Exception as e:
            results["table_judge"] = False
            logging.error(f"Table judge agent test failed: {e}")

        return results


# 全局Agent管理器实例
agent_manager = AgentManager()
