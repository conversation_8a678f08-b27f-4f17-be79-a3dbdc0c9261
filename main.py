"""
主程序 - VLM PDF2HTML 处理流程
"""

import logging
import argparse
import sys
from pathlib import Path
from typing import Optional

from langgraph.graph import StateGraph, END

from config import config
from state import State, StateManager
from nodes import graph_nodes, node_conditions
from utils import FileManager, ValidationHelper


class VLMPdf2HtmlProcessor:
    """VLM PDF转HTML处理器"""

    def __init__(self):
        """初始化处理器"""
        self.graph = self._build_graph()
        logging.info("VLM PDF2HTML Processor initialized")

    def _build_graph(self) -> StateGraph:
        """
        构建LANGGRAPH处理图

        图结构：
        START → load_pdf → process_page → [parse_pdf || render_prompt → judge_table]
        → extract_table_info → merge_decision → [save_result | merge_complete | merge_partial]
        → check_completion → [process_page | END]
        """
        # 创建StateGraph
        builder = StateGraph(State)

        # 添加节点
        builder.add_node("load_pdf", graph_nodes.load_pdf_node)
        builder.add_node("process_page", graph_nodes.process_page_node)
        builder.add_node("parse_pdf", graph_nodes.parse_pdf_node)
        builder.add_node("render_prompt", graph_nodes.render_prompt_node)
        builder.add_node("judge_table", graph_nodes.judge_table_node)
        builder.add_node("extract_table_info", graph_nodes.extract_table_info_node)
        builder.add_node("merge_decision", graph_nodes.merge_decision_node)
        builder.add_node("save_result", graph_nodes.save_result_node)
        builder.add_node("merge_complete_rows", graph_nodes.merge_complete_rows_node)
        builder.add_node("merge_partial_row", graph_nodes.merge_partial_row_node)
        builder.add_node("check_completion", graph_nodes.check_completion_node)

        # 设置入口点
        builder.set_entry_point("load_pdf")

        # 添加边
        # 基础流程
        builder.add_edge("load_pdf", "process_page")

        # 串行处理：先PDF解析，再表格判断
        builder.add_edge("process_page", "parse_pdf")
        builder.add_edge("parse_pdf", "render_prompt")
        builder.add_edge("render_prompt", "judge_table")

        # 继续后续处理
        builder.add_edge("judge_table", "extract_table_info")

        # 合并决策
        builder.add_edge("extract_table_info", "merge_decision")

        # 条件边：根据判断结果选择处理策略
        builder.add_conditional_edges(
            "merge_decision",
            node_conditions.get_merge_strategy,
            {
                "save_only": "save_result",
                "merge_complete": "merge_complete_rows",
                "merge_partial": "merge_partial_row",
            },
        )

        # 合并节点后的流程
        builder.add_edge("merge_complete_rows", "check_completion")
        builder.add_edge("merge_partial_row", "check_completion")
        builder.add_edge("save_result", "check_completion")

        # 循环或结束
        builder.add_conditional_edges(
            "check_completion",
            node_conditions.should_continue_processing,
            {"continue": "process_page", "END": END},
        )

        # 编译图
        return builder.compile()

    def process_pdf(self, pdf_path: str, output_dir: Optional[str] = None) -> dict:
        """
        处理PDF文件

        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录（可选）

        Returns:
            处理结果字典
        """
        try:
            # 验证输入
            if not ValidationHelper.validate_pdf_path(pdf_path):
                raise ValueError(f"Invalid PDF path: {pdf_path}")

            # 创建初始状态
            initial_state = StateManager.create_initial_state(pdf_path)

            logging.info(f"Starting PDF processing: {pdf_path}")

            # 执行图处理
            config = {"recursion_limit": 200}
            final_state = self.graph.invoke(initial_state, config=config)

            # 保存结果
            result = self._save_results(final_state, output_dir)

            # 生成处理报告
            report = self._generate_report(final_state)

            logging.info("PDF processing completed successfully")

            return {
                "success": True,
                "output_files": result,
                "report": report,
                "errors": final_state.get("errors", []),
                "warnings": final_state.get("warnings", []),
            }

        except Exception as e:
            error_msg = f"PDF processing failed: {e}"
            logging.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "output_files": [],
                "report": {},
                "errors": [error_msg],
                "warnings": [],
            }

    def _save_results(self, state: State, output_dir: Optional[str] = None) -> list:
        """保存处理结果"""
        try:
            # 确定输出目录
            if output_dir:
                output_path = Path(output_dir)
            else:
                output_path = FileManager.ensure_output_directory(state["pdf_path"])

            output_files = []

            # 保存合并后的HTML文件
            merged_htmls = state.get("merged_htmls", [])
            for i, html_content in enumerate(merged_htmls):
                if html_content.strip():
                    filename = f"page_{i+1:03d}.html"
                    file_path = output_path / filename
                    FileManager.write_text_file(html_content, str(file_path))
                    output_files.append(str(file_path))

            # 保存完整的HTML文件
            if merged_htmls:
                combined_html = self._combine_html_pages(merged_htmls)
                combined_file = output_path / "combined.html"
                FileManager.write_text_file(combined_html, str(combined_file))
                output_files.append(str(combined_file))

            # 保存处理报告
            report = self._generate_report(state)
            report_file = output_path / "processing_report.json"
            import json

            FileManager.write_text_file(
                json.dumps(report, ensure_ascii=False, indent=2), str(report_file)
            )
            output_files.append(str(report_file))

            return output_files

        except Exception as e:
            logging.error(f"Failed to save results: {e}")
            return []

    def _combine_html_pages(self, html_pages: list) -> str:
        """合并多个HTML页面为完整文档"""
        combined_content = []

        for i, html in enumerate(html_pages):
            if html.strip():
                combined_content.append(f"<!-- Page {i+1} -->")
                combined_content.append(html)
                combined_content.append("")

        # 创建完整的HTML文档
        full_html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF转换结果</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .page-break {{ page-break-before: always; margin-top: 40px; }}
    </style>
</head>
<body>
{chr(10).join(combined_content)}
</body>
</html>"""

        return full_html

    def _generate_report(self, state: State) -> dict:
        """生成处理报告"""
        progress_info = StateManager.get_progress_info(state)

        return {
            "pdf_path": state.get("pdf_path", ""),
            "total_pages": state.get("total_pages", 0),
            "processed_pages": progress_info["current_page"],
            "progress_percentage": progress_info["progress_percentage"],
            "judgment_results": state.get("table_judgment_results", []),
            "errors_count": len(state.get("errors", [])),
            "warnings_count": len(state.get("warnings", [])),
            "errors": state.get("errors", []),
            "warnings": state.get("warnings", []),
            "processing_complete": state.get("processing_complete", False),
        }


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description="VLM PDF2HTML 转换工具")
    parser.add_argument("pdf_path", help="PDF文件路径")
    parser.add_argument("-o", "--output", help="输出目录（可选）")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # 创建处理器
        processor = VLMPdf2HtmlProcessor()

        # 处理PDF
        result = processor.process_pdf(args.pdf_path, args.output)

        # 输出结果
        if result["success"]:
            print(f"✅ PDF处理成功!")
            print(f"📁 输出文件: {len(result['output_files'])} 个")
            for file_path in result["output_files"]:
                print(f"   - {file_path}")

            if result["warnings"]:
                print(f"⚠️  警告: {len(result['warnings'])} 个")
                for warning in result["warnings"]:
                    print(f"   - {warning}")
        else:
            print(f"❌ PDF处理失败: {result['error']}")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⏹️  处理被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
