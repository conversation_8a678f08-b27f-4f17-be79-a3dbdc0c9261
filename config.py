"""
配置文件 - VLM PDF2HTML 项目配置
"""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Config:
    """项目配置类"""

    # ==================== VLM 模型配置 ====================
    # Qwen-VL 配置
    QWEN_VL_API_KEY: str = "sk-6119bb6e5a95440598ef80c25a40dc8e"
    QWEN_VL_BASE_URL: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    QWEN_VL_MODEL: str = "qwen-vl-max"

    # 模型参数
    MODEL_TEMPERATURE: float = 0.1
    MODEL_MAX_TOKENS: int = 4000
    MODEL_TIMEOUT: int = 180  # 秒，增加到3分钟以应对VLM处理时间

    # ==================== 路径配置 ====================
    # 项目根目录
    PROJECT_ROOT: Path = Path(__file__).parent

    # Prompt 文件目录
    PROMPT_DIR: Path = PROJECT_ROOT / "prompt"
    PARSE_PDF_PROMPT_PATH: Path = PROMPT_DIR / "PARSER_PDF.md"
    TABLE_JUDGE_PROMPT_PATH: Path = PROMPT_DIR / "TABLE_JUDGE.md"

    # 输出目录
    OUTPUT_DIR: Path = PROJECT_ROOT / "output"
    TEMP_DIR: Path = PROJECT_ROOT / "temp"

    # ==================== PDF 处理配置 ====================
    # PDF 转图像配置
    PDF_DPI: int = 300  # 图像分辨率
    PDF_FORMAT: str = "PNG"  # 输出格式
    PDF_THREAD_COUNT: int = 1  # 处理线程数

    # 图像处理配置
    MAX_IMAGE_SIZE: tuple = (2048, 2048)  # 最大图像尺寸
    IMAGE_QUALITY: int = 95  # 图像质量

    # ==================== 日志配置 ====================
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = (
        "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    )
    LOG_FILE: Optional[Path] = PROJECT_ROOT / "logs" / "vlm_pdf2html.log"

    # ==================== 处理配置 ====================
    # 重试配置
    MAX_RETRIES: int = 2  # 减少重试次数，因为单次请求时间较长
    RETRY_DELAY: float = 2.0  # 秒，增加重试间隔

    # 并发配置
    MAX_CONCURRENT_PAGES: int = 1  # 同时处理的页面数

    # 表格合并配置
    TABLE_MERGE_VALIDATION: bool = True  # 是否验证合并结果

    @classmethod
    def ensure_directories(cls) -> None:
        """确保必要的目录存在"""
        directories = [
            cls.OUTPUT_DIR,
            cls.TEMP_DIR,
            cls.LOG_FILE.parent if cls.LOG_FILE else None,
        ]

        for directory in directories:
            if directory and not directory.exists():
                directory.mkdir(parents=True, exist_ok=True)

    @classmethod
    def validate_config(cls) -> bool:
        """验证配置的有效性"""
        # 检查必要的文件是否存在
        required_files = [cls.PARSE_PDF_PROMPT_PATH, cls.TABLE_JUDGE_PROMPT_PATH]

        for file_path in required_files:
            if not file_path.exists():
                raise FileNotFoundError(f"Required file not found: {file_path}")

        # 检查API密钥
        if not cls.QWEN_VL_API_KEY:
            raise ValueError("QWEN_VL_API_KEY is required")

        return True

    @classmethod
    def get_model_config(cls) -> dict:
        """获取模型配置字典"""
        return {
            "api_key": cls.QWEN_VL_API_KEY,
            "base_url": cls.QWEN_VL_BASE_URL,
            "model": cls.QWEN_VL_MODEL,
            "temperature": cls.MODEL_TEMPERATURE,
            "max_tokens": cls.MODEL_MAX_TOKENS,
            "timeout": cls.MODEL_TIMEOUT,
        }


# 全局配置实例
config = Config()

# 在导入时验证配置和创建目录
if __name__ != "__main__":
    try:
        config.ensure_directories()
        config.validate_config()
    except Exception as e:
        print(f"Configuration error: {e}")
        raise
