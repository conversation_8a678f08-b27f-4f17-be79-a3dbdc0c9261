"""
状态管理 - 定义LANGGRAPH流程中的状态类
"""

from typing import List, Optional, Any, TypedDict, Annotated
from pathlib import Path
import base64
from PIL import Image
import io
import operator


class State(TypedDict):
    """
    LANGGRAPH流程状态类

    管理整个PDF解析流程中的所有状态数据，支持页面间的数据传递和续表合并
    """

    # ==================== 基础信息 ====================
    pdf_path: str  # PDF文件路径
    current_page: int  # 当前处理的页面号（从0开始）
    total_pages: int  # PDF总页数
    processing_complete: bool  # 是否处理完成

    # ==================== 页面数据 ====================
    page_images: List[Any]  # 所有页面的图像对象列表
    current_page_image: Optional[Any]  # 当前页面的图像对象
    current_page_image_base64: Optional[str]  # 当前页面图像的base64编码

    # ==================== HTML解析结果 ====================
    page_htmls: List[str]  # 每页解析出的原始HTML
    current_page_html: Optional[str]  # 当前页面解析的HTML
    merged_htmls: List[str]  # 合并续表后的最终HTML结果

    # ==================== 续表判断相关 ====================
    table_judgment_results: List[
        str
    ]  # 每页的判断结果：["新表格", "续表，开始新行", "续表，合并首行"]
    current_judgment_result: Optional[str]  # 当前页面的判断结果

    # ==================== 表格数据传递 ====================
    extracted_table_last_rows: List[str]  # 每页表格的最后一行数据（用于下一页判断）
    last_page_table_last_row: Optional[str]  # 上一页表格的最后一行数据
    current_page_table_last_row: Optional[str]  # 当前页面表格的最后一行数据

    # ==================== 模板渲染 ====================
    rendered_table_judge_prompt: Optional[str]  # 渲染后的TABLE_JUDGE prompt

    # ==================== 错误处理 ====================
    errors: Annotated[List[str], operator.add]  # 处理过程中的错误信息
    warnings: Annotated[List[str], operator.add]  # 处理过程中的警告信息


class StateManager:
    """状态管理器 - 提供状态操作的便捷方法"""

    @staticmethod
    def create_initial_state(pdf_path: str) -> State:
        """创建初始状态"""
        return State(
            # 基础信息
            pdf_path=pdf_path,
            current_page=0,
            total_pages=0,
            processing_complete=False,
            # 页面数据
            page_images=[],
            current_page_image=None,
            current_page_image_base64=None,
            # HTML解析结果
            page_htmls=[],
            current_page_html=None,
            merged_htmls=[],
            # 续表判断相关
            table_judgment_results=[],
            current_judgment_result=None,
            # 表格数据传递
            extracted_table_last_rows=[],
            last_page_table_last_row=None,
            current_page_table_last_row=None,
            # 模板渲染
            rendered_table_judge_prompt=None,
            # 错误处理
            errors=[],
            warnings=[],
        )

    @staticmethod
    def image_to_base64(image: Image.Image) -> str:
        """将PIL图像转换为base64编码"""
        buffer = io.BytesIO()
        image.save(buffer, format="PNG")
        image_bytes = buffer.getvalue()
        return base64.b64encode(image_bytes).decode("utf-8")

    @staticmethod
    def update_current_page(state: State, page_num: int) -> State:
        """更新当前页面信息"""
        state["current_page"] = page_num

        # 设置当前页面图像
        if page_num < len(state["page_images"]):
            state["current_page_image"] = state["page_images"][page_num]
            state["current_page_image_base64"] = StateManager.image_to_base64(
                state["page_images"][page_num]
            )

        # 设置上一页表格最后一行数据
        if page_num > 0 and len(state["extracted_table_last_rows"]) >= page_num:
            state["last_page_table_last_row"] = state["extracted_table_last_rows"][
                page_num - 1
            ]
        else:
            state["last_page_table_last_row"] = None

        return state

    @staticmethod
    def save_page_result(
        state: State, html: str, judgment: str, table_last_row: str
    ) -> State:
        """保存当前页面的处理结果"""
        # 保存HTML结果
        state["page_htmls"].append(html)
        state["current_page_html"] = html

        # 保存判断结果
        state["table_judgment_results"].append(judgment)
        state["current_judgment_result"] = judgment

        # 保存表格最后一行数据
        state["extracted_table_last_rows"].append(table_last_row)
        state["current_page_table_last_row"] = table_last_row

        return state

    @staticmethod
    def save_merged_result(state: State, merged_html: str) -> State:
        """保存合并后的HTML结果"""
        state["merged_htmls"].append(merged_html)
        return state

    @staticmethod
    def add_error(state: State, error_msg: str) -> State:
        """添加错误信息"""
        state["errors"].append(f"Page {state['current_page']}: {error_msg}")
        return state

    @staticmethod
    def add_warning(state: State, warning_msg: str) -> State:
        """添加警告信息"""
        state["warnings"].append(f"Page {state['current_page']}: {warning_msg}")
        return state

    @staticmethod
    def is_processing_complete(state: State) -> bool:
        """检查是否处理完成"""
        return state["current_page"] >= state["total_pages"] - 1

    @staticmethod
    def get_progress_info(state: State) -> dict:
        """获取处理进度信息"""
        return {
            "current_page": state["current_page"] + 1,  # 显示时从1开始
            "total_pages": state["total_pages"],
            "progress_percentage": (state["current_page"] + 1)
            / state["total_pages"]
            * 100,
            "errors_count": len(state["errors"]),
            "warnings_count": len(state["warnings"]),
        }
