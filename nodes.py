"""
LANGGRAPH节点实现 - 定义图中的所有处理节点
"""

import logging

from state import State, StateManager
from agents import agent_manager
from utils import PDFProcessor, ValidationHelper
from table_merger import table_merger
from prompt_renderer import prompt_renderer


class GraphNodes:
    """图节点集合 - 包含所有LANGGRAPH节点的实现"""

    @staticmethod
    def load_pdf_node(state: State) -> State:
        """
        加载PDF节点 - 将PDF转换为图像列表

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            logging.info(f"Loading PDF: {state['pdf_path']}")

            # 验证PDF路径
            if not ValidationHelper.validate_pdf_path(state["pdf_path"]):
                error_msg = f"Invalid PDF path: {state['pdf_path']}"
                logging.error(error_msg)
                return StateManager.add_error(state, error_msg)

            # 转换PDF为图像
            images, total_pages = PDFProcessor.load_pdf_to_images(state["pdf_path"])

            # 更新状态
            state["page_images"] = images
            state["total_pages"] = total_pages
            state["current_page"] = 0

            logging.info(f"PDF loaded successfully: {total_pages} pages")

            return state

        except Exception as e:
            error_msg = f"Failed to load PDF: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)

    @staticmethod
    def process_page_node(state: State) -> State:
        """
        处理页面节点 - 准备处理当前页面

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            current_page = state["current_page"]
            total_pages = state["total_pages"]

            logging.info(f"Processing page {current_page + 1}/{total_pages}")

            # 检查是否还有页面需要处理
            if current_page >= total_pages:
                state["processing_complete"] = True
                return state

            # 更新当前页面信息
            state = StateManager.update_current_page(state, current_page)

            return state

        except Exception as e:
            error_msg = f"Failed to process page: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)

    @staticmethod
    def parse_pdf_node(state: State) -> State:
        """
        PDF解析节点 - 使用PDF解析Agent将页面转换为HTML

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            current_page = state["current_page"]
            image_base64 = state["current_page_image_base64"]

            if not image_base64:
                error_msg = "No image data for current page"
                logging.error(error_msg)
                return StateManager.add_error(state, error_msg)

            logging.info(f"Parsing PDF page {current_page + 1}")

            # 调用PDF解析Agent
            pdf_parser = agent_manager.get_pdf_parser()
            html_content = pdf_parser.parse_page(image_base64)

            # 验证HTML内容
            if not ValidationHelper.validate_html_content(html_content):
                warning_msg = "Generated HTML content may be invalid"
                logging.warning(warning_msg)
                state = StateManager.add_warning(state, warning_msg)

            # 保存解析结果
            state["current_page_html"] = html_content

            logging.info(f"PDF page {current_page + 1} parsed successfully")

            return state

        except Exception as e:
            error_msg = f"Failed to parse PDF page: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)

    @staticmethod
    def render_prompt_node(state: State) -> State:
        """
        渲染Prompt节点 - 为表格判断准备动态prompt

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            current_page = state["current_page"]
            last_page_table_last_row = state.get("last_page_table_last_row")

            logging.info(f"Rendering prompt for page {current_page + 1}")

            # 渲染TABLE_JUDGE prompt
            rendered_prompt = prompt_renderer.render_table_judge_prompt(
                last_page_table_last_row
            )

            # 保存渲染结果
            state["rendered_table_judge_prompt"] = rendered_prompt

            logging.info(f"Prompt rendered successfully for page {current_page + 1}")

            return state

        except Exception as e:
            error_msg = f"Failed to render prompt: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)

    @staticmethod
    def judge_table_node(state: State) -> State:
        """
        表格判断节点 - 判断续表情况

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            current_page = state["current_page"]
            image_base64 = state["current_page_image_base64"]
            last_page_table_last_row = state.get("last_page_table_last_row")

            if not image_base64:
                error_msg = "No image data for current page"
                logging.error(error_msg)
                return StateManager.add_error(state, error_msg)

            logging.info(f"Judging table continuation for page {current_page + 1}")

            # 调用表格判断Agent
            table_judge = agent_manager.get_table_judge()
            judgment_result = table_judge.judge_table_continuation(
                image_base64, last_page_table_last_row
            )

            # 验证判断结果
            if not ValidationHelper.validate_judgment_result(judgment_result):
                warning_msg = f"Invalid judgment result format: {judgment_result}"
                logging.warning(warning_msg)
                state = StateManager.add_warning(state, warning_msg)
                # 默认设为新表格
                judgment_result = "新表格"

            # 保存判断结果
            state["current_judgment_result"] = judgment_result

            logging.info(
                f"Table judgment for page {current_page + 1}: {judgment_result}"
            )

            return state

        except Exception as e:
            error_msg = f"Failed to judge table: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)

    @staticmethod
    def extract_table_info_node(state: State) -> State:
        """
        提取表格信息节点 - 提取当前页表格的最后一行数据

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            current_page = state["current_page"]
            current_html = state.get("current_page_html", "")

            logging.info(f"Extracting table info for page {current_page + 1}")

            # 提取表格最后一行数据
            table_last_row = table_merger.extract_table_last_row(current_html)

            # 保存提取结果
            state["current_page_table_last_row"] = table_last_row

            logging.info(f"Table info extracted for page {current_page + 1}")

            return state

        except Exception as e:
            error_msg = f"Failed to extract table info: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)

    @staticmethod
    def merge_decision_node(state: State) -> State:
        """
        合并决策节点 - 根据判断结果决定下一步处理

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            current_page = state["current_page"]
            judgment_result = state.get("current_judgment_result", "新表格")

            logging.info(
                f"Making merge decision for page {current_page + 1}: {judgment_result}"
            )

            # 决策逻辑在图的边条件中处理，这里只是记录
            state["merge_decision"] = judgment_result

            return state

        except Exception as e:
            error_msg = f"Failed to make merge decision: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)

    @staticmethod
    def save_result_node(state: State) -> State:
        """
        保存结果节点 - 保存当前页面的处理结果

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            current_page = state["current_page"]
            current_html = state.get("current_page_html", "")
            judgment_result = state.get("current_judgment_result", "新表格")
            table_last_row = state.get("current_page_table_last_row", "")

            logging.info(f"Saving result for page {current_page + 1}")

            # 保存页面结果
            state = StateManager.save_page_result(
                state, current_html, judgment_result, table_last_row
            )

            # 如果是新表格，直接保存到merged_htmls
            if judgment_result == "新表格":
                state = StateManager.save_merged_result(state, current_html)

            logging.info(f"Result saved for page {current_page + 1}")

            return state

        except Exception as e:
            error_msg = f"Failed to save result: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)

    @staticmethod
    def merge_complete_rows_node(state: State) -> State:
        """
        合并完整行节点 - 处理"续表，开始新行"情况

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            current_page = state["current_page"]
            current_html = state.get("current_page_html", "")

            logging.info(f"Merging complete rows for page {current_page + 1}")

            # 获取上一页的HTML
            previous_html = ""
            if len(state["merged_htmls"]) > 0:
                previous_html = state["merged_htmls"][-1]

            # 执行合并
            merged_html, table_last_row = table_merger.process_page_result(
                current_html, "续表，开始新行", previous_html
            )

            # 验证合并结果
            if table_merger.validate_merge_result(merged_html):
                # 更新上一页的合并结果
                if len(state["merged_htmls"]) > 0:
                    state["merged_htmls"][-1] = merged_html
                else:
                    state = StateManager.save_merged_result(state, merged_html)

                # 更新表格最后一行数据
                state["current_page_table_last_row"] = table_last_row

                logging.info(
                    f"Complete rows merged successfully for page {current_page + 1}"
                )
            else:
                warning_msg = "Merge validation failed, using original HTML"
                logging.warning(warning_msg)
                state = StateManager.add_warning(state, warning_msg)
                state = StateManager.save_merged_result(state, current_html)

            return state

        except Exception as e:
            error_msg = f"Failed to merge complete rows: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)

    @staticmethod
    def merge_partial_row_node(state: State) -> State:
        """
        合并部分行节点 - 处理"续表，合并首行"情况

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            current_page = state["current_page"]
            current_html = state.get("current_page_html", "")

            logging.info(f"Merging partial row for page {current_page + 1}")

            # 获取上一页的HTML
            previous_html = ""
            if len(state["merged_htmls"]) > 0:
                previous_html = state["merged_htmls"][-1]

            # 执行合并
            merged_html, table_last_row = table_merger.process_page_result(
                current_html, "续表，合并首行", previous_html
            )

            # 验证合并结果
            if table_merger.validate_merge_result(merged_html):
                # 更新上一页的合并结果
                if len(state["merged_htmls"]) > 0:
                    state["merged_htmls"][-1] = merged_html
                else:
                    state = StateManager.save_merged_result(state, merged_html)

                # 更新表格最后一行数据
                state["current_page_table_last_row"] = table_last_row

                logging.info(
                    f"Partial row merged successfully for page {current_page + 1}"
                )
            else:
                warning_msg = "Merge validation failed, using original HTML"
                logging.warning(warning_msg)
                state = StateManager.add_warning(state, warning_msg)
                state = StateManager.save_merged_result(state, current_html)

            return state

        except Exception as e:
            error_msg = f"Failed to merge partial row: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)

    @staticmethod
    def check_completion_node(state: State) -> State:
        """
        检查完成节点 - 检查是否处理完所有页面

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            current_page = state["current_page"]
            total_pages = state["total_pages"]

            logging.info(f"Checking completion: page {current_page + 1}/{total_pages}")

            # 检查是否处理完成
            if current_page >= total_pages - 1:
                state["processing_complete"] = True
                logging.info("All pages processed successfully")
            else:
                # 移动到下一页
                state["current_page"] = current_page + 1
                logging.info(f"Moving to next page: {state['current_page'] + 1}")

            return state

        except Exception as e:
            error_msg = f"Failed to check completion: {e}"
            logging.error(error_msg)
            return StateManager.add_error(state, error_msg)


class NodeConditions:
    """节点条件 - 定义图中的条件边"""

    @staticmethod
    def should_continue_processing(state: State) -> str:
        """判断是否继续处理"""
        if state.get("processing_complete", False):
            return "END"
        else:
            return "continue"

    @staticmethod
    def get_merge_strategy(state: State) -> str:
        """根据判断结果选择合并策略"""
        judgment_result = state.get("current_judgment_result", "新表格")

        if judgment_result == "新表格":
            return "save_only"
        elif judgment_result == "续表，开始新行":
            return "merge_complete"
        elif judgment_result == "续表，合并首行":
            return "merge_partial"
        else:
            # 默认情况
            return "save_only"


# 全局节点实例
graph_nodes = GraphNodes()
node_conditions = NodeConditions()
