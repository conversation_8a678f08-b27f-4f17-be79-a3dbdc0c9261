"""
表格合并器 - 处理三种续表合并情况
"""
import logging
import re
from typing import Optional, List, Tuple, Dict
from bs4 import BeautifulSoup, Tag, NavigableString
from bs4.element import ResultSet

from config import config
from utils import ValidationHelper


class TableMerger:
    """
    表格合并器
    
    支持三种续表处理情况：
    1. 新表格 - 不合并，直接保存
    2. 续表，开始新行 - 将当前页所有表格行追加到上一页表格末尾
    3. 续表，合并首行 - 先合并第一行数据，再追加其余行
    """
    
    def __init__(self):
        """初始化表格合并器"""
        self.parser = 'lxml'  # 使用lxml解析器以获得更好的性能
    
    def process_page_result(self, 
                          current_page_html: str, 
                          judgment_result: str, 
                          previous_page_html: Optional[str] = None) -> Tuple[str, str]:
        """
        处理页面结果
        
        Args:
            current_page_html: 当前页面的HTML
            judgment_result: 判断结果
            previous_page_html: 上一页的HTML（如果需要合并）
            
        Returns:
            (合并后的HTML, 当前页表格最后一行数据)
        """
        try:
            # 提取当前页表格的最后一行数据
            current_table_last_row = self.extract_table_last_row(current_page_html)
            
            if judgment_result == "新表格":
                # 新表格，不需要合并
                return current_page_html, current_table_last_row
            
            elif judgment_result == "续表，开始新行":
                # 续表，追加完整新行
                if previous_page_html:
                    merged_html = self.append_complete_rows(previous_page_html, current_page_html)
                    return merged_html, current_table_last_row
                else:
                    logging.warning("No previous page HTML for continuation table")
                    return current_page_html, current_table_last_row
            
            elif judgment_result == "续表，合并首行":
                # 续表，合并首行
                if previous_page_html:
                    merged_html = self.merge_partial_row(previous_page_html, current_page_html)
                    return merged_html, current_table_last_row
                else:
                    logging.warning("No previous page HTML for partial row merge")
                    return current_page_html, current_table_last_row
            
            else:
                logging.error(f"Unknown judgment result: {judgment_result}")
                return current_page_html, current_table_last_row
                
        except Exception as e:
            logging.error(f"Table processing failed: {e}")
            return current_page_html, ""
    
    def extract_table_last_row(self, html: str) -> str:
        """
        提取HTML中表格的最后一行数据
        
        Args:
            html: HTML内容
            
        Returns:
            表格最后一行的文本数据
        """
        try:
            if not html or not html.strip():
                return ""
            
            soup = BeautifulSoup(html, self.parser)
            tables = soup.find_all('table')
            
            if not tables:
                return ""
            
            # 获取最后一个表格
            last_table = tables[-1]
            
            # 查找表格中的所有行
            rows = last_table.find_all('tr')
            
            if not rows:
                return ""
            
            # 获取最后一行
            last_row = rows[-1]
            
            # 提取行中的文本数据
            cells = last_row.find_all(['td', 'th'])
            cell_texts = [cell.get_text(strip=True) for cell in cells]
            
            # 合并单元格文本
            row_text = " | ".join(cell_texts)
            
            return row_text
            
        except Exception as e:
            logging.error(f"Failed to extract table last row: {e}")
            return ""
    
    def find_last_table(self, html: str) -> Optional[Tag]:
        """
        在HTML中找到最后一个表格
        
        Args:
            html: HTML内容
            
        Returns:
            最后一个表格的Tag对象
        """
        try:
            soup = BeautifulSoup(html, self.parser)
            tables = soup.find_all('table')
            return tables[-1] if tables else None
        except Exception as e:
            logging.error(f"Failed to find last table: {e}")
            return None
    
    def append_complete_rows(self, previous_html: str, current_html: str) -> str:
        """
        追加完整新行到上一页表格
        
        Args:
            previous_html: 上一页的HTML
            current_html: 当前页的HTML
            
        Returns:
            合并后的HTML
        """
        try:
            # 解析HTML
            prev_soup = BeautifulSoup(previous_html, self.parser)
            curr_soup = BeautifulSoup(current_html, self.parser)
            
            # 找到上一页的最后一个表格
            prev_last_table = self.find_last_table(str(prev_soup))
            if not prev_last_table:
                logging.warning("No table found in previous page")
                return previous_html
            
            # 找到当前页的第一个表格
            curr_tables = curr_soup.find_all('table')
            if not curr_tables:
                logging.warning("No table found in current page")
                return previous_html
            
            curr_first_table = curr_tables[0]
            
            # 提取当前页表格的所有数据行（跳过表头）
            curr_rows = curr_first_table.find_all('tr')
            
            # 判断第一行是否为表头
            data_rows = self._filter_data_rows(curr_rows)
            
            # 将数据行追加到上一页表格
            prev_tbody = prev_last_table.find('tbody')
            if not prev_tbody:
                # 如果没有tbody，创建一个
                prev_tbody = prev_soup.new_tag('tbody')
                prev_last_table.append(prev_tbody)
            
            # 追加行
            for row in data_rows:
                # 创建新行的副本
                new_row = prev_soup.new_tag('tr')
                for cell in row.find_all(['td', 'th']):
                    new_cell = prev_soup.new_tag('td')
                    new_cell.string = cell.get_text(strip=True)
                    # 复制属性
                    for attr, value in cell.attrs.items():
                        if attr in ['rowspan', 'colspan', 'class', 'style']:
                            new_cell[attr] = value
                    new_row.append(new_cell)
                
                prev_tbody.append(new_row)
            
            return str(prev_soup)
            
        except Exception as e:
            logging.error(f"Failed to append complete rows: {e}")
            return previous_html
    
    def merge_partial_row(self, previous_html: str, current_html: str) -> str:
        """
        合并被截断的行数据
        
        Args:
            previous_html: 上一页的HTML
            current_html: 当前页的HTML
            
        Returns:
            合并后的HTML
        """
        try:
            # 解析HTML
            prev_soup = BeautifulSoup(previous_html, self.parser)
            curr_soup = BeautifulSoup(current_html, self.parser)
            
            # 找到上一页的最后一个表格
            prev_last_table = self.find_last_table(str(prev_soup))
            if not prev_last_table:
                logging.warning("No table found in previous page")
                return previous_html
            
            # 找到当前页的第一个表格
            curr_tables = curr_soup.find_all('table')
            if not curr_tables:
                logging.warning("No table found in current page")
                return previous_html
            
            curr_first_table = curr_tables[0]
            
            # 获取上一页表格的最后一行
            prev_rows = prev_last_table.find_all('tr')
            if not prev_rows:
                logging.warning("No rows found in previous table")
                return previous_html
            
            prev_last_row = prev_rows[-1]
            
            # 获取当前页表格的第一行
            curr_rows = curr_first_table.find_all('tr')
            if not curr_rows:
                logging.warning("No rows found in current table")
                return previous_html
            
            curr_first_row = curr_rows[0]
            
            # 合并行数据
            self._merge_row_cells(prev_last_row, curr_first_row)
            
            # 追加当前页的其余行（如果有）
            remaining_rows = curr_rows[1:]
            if remaining_rows:
                prev_tbody = prev_last_table.find('tbody')
                if not prev_tbody:
                    prev_tbody = prev_soup.new_tag('tbody')
                    prev_last_table.append(prev_tbody)
                
                for row in remaining_rows:
                    new_row = prev_soup.new_tag('tr')
                    for cell in row.find_all(['td', 'th']):
                        new_cell = prev_soup.new_tag('td')
                        new_cell.string = cell.get_text(strip=True)
                        for attr, value in cell.attrs.items():
                            if attr in ['rowspan', 'colspan', 'class', 'style']:
                                new_cell[attr] = value
                        new_row.append(new_cell)
                    prev_tbody.append(new_row)
            
            return str(prev_soup)
            
        except Exception as e:
            logging.error(f"Failed to merge partial row: {e}")
            return previous_html
    
    def _filter_data_rows(self, rows: List[Tag]) -> List[Tag]:
        """过滤出数据行（排除表头）"""
        if not rows:
            return []
        
        # 简单的表头检测：如果第一行包含th标签或者样式明显不同，则跳过
        first_row = rows[0]
        if first_row.find('th') or self._is_header_row(first_row):
            return rows[1:]
        
        return rows
    
    def _is_header_row(self, row: Tag) -> bool:
        """判断是否为表头行"""
        # 检查样式特征
        style = row.get('style', '')
        class_attr = row.get('class', [])
        
        # 常见的表头特征
        header_indicators = [
            'font-weight:bold', 'font-weight: bold',
            'background-color', 'background',
            'text-align:center', 'text-align: center'
        ]
        
        for indicator in header_indicators:
            if indicator in style:
                return True
        
        # 检查class属性
        if isinstance(class_attr, list):
            header_classes = ['header', 'thead', 'th']
            if any(cls in ' '.join(class_attr) for cls in header_classes):
                return True
        
        return False
    
    def _merge_row_cells(self, prev_row: Tag, curr_row: Tag) -> None:
        """合并两行的单元格数据"""
        prev_cells = prev_row.find_all(['td', 'th'])
        curr_cells = curr_row.find_all(['td', 'th'])
        
        # 合并对应位置的单元格
        min_cells = min(len(prev_cells), len(curr_cells))
        
        for i in range(min_cells):
            prev_cell = prev_cells[i]
            curr_cell = curr_cells[i]
            
            # 合并文本内容
            prev_text = prev_cell.get_text(strip=True)
            curr_text = curr_cell.get_text(strip=True)
            
            # 如果上一行的单元格以不完整的方式结束，则合并
            if prev_text and curr_text:
                merged_text = prev_text + curr_text
                prev_cell.string = merged_text
    
    def validate_merge_result(self, html: str) -> bool:
        """验证合并结果的完整性"""
        try:
            if not ValidationHelper.validate_html_content(html):
                return False
            
            soup = BeautifulSoup(html, self.parser)
            tables = soup.find_all('table')
            
            # 检查表格结构的完整性
            for table in tables:
                rows = table.find_all('tr')
                if not rows:
                    continue
                
                # 检查每行的单元格数量是否一致
                cell_counts = []
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    cell_count = sum(int(cell.get('colspan', 1)) for cell in cells)
                    cell_counts.append(cell_count)
                
                # 如果单元格数量差异过大，可能存在问题
                if cell_counts and max(cell_counts) - min(cell_counts) > 2:
                    logging.warning(f"Inconsistent cell counts in table: {cell_counts}")
            
            return True
            
        except Exception as e:
            logging.error(f"Merge validation failed: {e}")
            return False


# 全局表格合并器实例
table_merger = TableMerger()
