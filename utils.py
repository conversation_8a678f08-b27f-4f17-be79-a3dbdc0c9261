"""
工具函数 - PDF处理、图像处理、文件操作等基础功能
"""

import os
import logging
from typing import List, Optional, Tuple
from pathlib import Path
import fitz  # PyMuPDF
from PIL import Image
import pdf2image
import io
import base64

from config import config


class PDFProcessor:
    """PDF处理器 - 负责PDF文件的加载和转换"""

    @staticmethod
    def load_pdf_to_images(pdf_path: str) -> Tuple[List[Image.Image], int]:
        """
        将PDF转换为图像列表

        Args:
            pdf_path: PDF文件路径

        Returns:
            (图像列表, 总页数)
        """
        try:
            pdf_path = Path(pdf_path)
            if not pdf_path.exists():
                raise FileNotFoundError(f"PDF file not found: {pdf_path}")

            # 使用pdf2image转换
            images = pdf2image.convert_from_path(
                pdf_path,
                dpi=config.PDF_DPI,
                fmt=config.PDF_FORMAT,
                thread_count=config.PDF_THREAD_COUNT,
            )

            # 调整图像尺寸
            processed_images = []
            for img in images:
                processed_img = PDFProcessor._resize_image(img, config.MAX_IMAGE_SIZE)
                processed_images.append(processed_img)

            total_pages = len(processed_images)
            logging.info(f"Successfully loaded PDF: {pdf_path}, {total_pages} pages")

            return processed_images, total_pages

        except Exception as e:
            logging.error(f"Failed to load PDF {pdf_path}: {e}")
            raise RuntimeError(f"PDF processing failed: {e}")

    @staticmethod
    def _resize_image(image: Image.Image, max_size: Tuple[int, int]) -> Image.Image:
        """调整图像尺寸"""
        if image.size[0] <= max_size[0] and image.size[1] <= max_size[1]:
            return image

        # 计算缩放比例
        ratio = min(max_size[0] / image.size[0], max_size[1] / image.size[1])
        new_size = (int(image.size[0] * ratio), int(image.size[1] * ratio))

        return image.resize(new_size, Image.Resampling.LANCZOS)

    @staticmethod
    def get_pdf_info(pdf_path: str) -> dict:
        """获取PDF基本信息"""
        try:
            doc = fitz.open(pdf_path)
            info = {
                "page_count": doc.page_count,
                "metadata": doc.metadata,
                "file_size": os.path.getsize(pdf_path),
            }
            doc.close()
            return info
        except Exception as e:
            logging.error(f"Failed to get PDF info: {e}")
            return {}


class ImageProcessor:
    """图像处理器 - 图像格式转换和编码"""

    @staticmethod
    def image_to_base64(image: Image.Image, format: str = "PNG") -> str:
        """将PIL图像转换为base64编码"""
        try:
            buffer = io.BytesIO()
            image.save(buffer, format=format, quality=config.IMAGE_QUALITY)
            image_bytes = buffer.getvalue()
            return base64.b64encode(image_bytes).decode("utf-8")
        except Exception as e:
            logging.error(f"Failed to convert image to base64: {e}")
            raise

    @staticmethod
    def base64_to_image(base64_str: str) -> Image.Image:
        """将base64编码转换为PIL图像"""
        try:
            image_bytes = base64.b64decode(base64_str)
            return Image.open(io.BytesIO(image_bytes))
        except Exception as e:
            logging.error(f"Failed to convert base64 to image: {e}")
            raise

    @staticmethod
    def save_image(image: Image.Image, output_path: str) -> None:
        """保存图像到文件"""
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            image.save(output_path, quality=config.IMAGE_QUALITY)
            logging.info(f"Image saved: {output_path}")
        except Exception as e:
            logging.error(f"Failed to save image: {e}")
            raise


class FileManager:
    """文件管理器 - 文件读写和路径管理"""

    @staticmethod
    def read_text_file(file_path: str) -> str:
        """读取文本文件"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            logging.error(f"Failed to read file {file_path}: {e}")
            raise

    @staticmethod
    def write_text_file(content: str, file_path: str) -> None:
        """写入文本文件"""
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)

            logging.info(f"File written: {file_path}")
        except Exception as e:
            logging.error(f"Failed to write file {file_path}: {e}")
            raise

    @staticmethod
    def ensure_output_directory(pdf_path: str) -> Path:
        """为PDF文件创建输出目录"""
        pdf_name = Path(pdf_path).stem
        output_dir = config.OUTPUT_DIR / pdf_name
        output_dir.mkdir(parents=True, exist_ok=True)
        return output_dir

    @staticmethod
    def get_temp_file_path(prefix: str, suffix: str = ".tmp") -> Path:
        """获取临时文件路径"""
        config.TEMP_DIR.mkdir(parents=True, exist_ok=True)
        import uuid

        filename = f"{prefix}_{uuid.uuid4().hex[:8]}{suffix}"
        return config.TEMP_DIR / filename


class LoggingSetup:
    """日志配置器"""

    @staticmethod
    def setup_logging() -> None:
        """配置日志系统"""
        # 确保日志目录存在
        if config.LOG_FILE:
            config.LOG_FILE.parent.mkdir(parents=True, exist_ok=True)

        # 配置日志格式
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL.upper()),
            format="%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d | %(message)s",
            handlers=[
                logging.StreamHandler(),  # 控制台输出
                (
                    logging.FileHandler(config.LOG_FILE)
                    if config.LOG_FILE
                    else logging.NullHandler()
                ),
            ],
        )

        # 设置第三方库的日志级别
        logging.getLogger("PIL").setLevel(logging.WARNING)
        logging.getLogger("pdf2image").setLevel(logging.WARNING)
        logging.getLogger("httpcore").setLevel(logging.WARNING)
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("openai").setLevel(logging.WARNING)


class RetryHelper:
    """重试助手 - 提供重试机制"""

    @staticmethod
    def retry_on_failure(func, max_retries: int = None, delay: float = None):
        """
        重试装饰器

        Args:
            func: 要重试的函数
            max_retries: 最大重试次数
            delay: 重试间隔
        """
        import time
        import functools

        max_retries = max_retries or config.MAX_RETRIES
        delay = delay or config.RETRY_DELAY

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logging.warning(
                            f"Attempt {attempt + 1} failed: {e}. Retrying in {delay}s..."
                        )
                        time.sleep(delay)
                    else:
                        logging.error(
                            f"All {max_retries + 1} attempts failed. Last error: {e}"
                        )

            raise last_exception

        return wrapper


class ValidationHelper:
    """验证助手 - 数据验证功能"""

    @staticmethod
    def validate_pdf_path(pdf_path: str) -> bool:
        """验证PDF文件路径"""
        try:
            path = Path(pdf_path)
            return path.exists() and path.suffix.lower() == ".pdf"
        except Exception:
            return False

    @staticmethod
    def validate_html_content(html: str) -> bool:
        """验证HTML内容的基本格式"""
        if not html or not html.strip():
            return False

        # 基本的HTML标签检查
        basic_tags = ["<p>", "<table>", "<h1>", "<h2>", "<h3>", "<h4>", "<h5>", "<h6>"]
        return any(tag in html for tag in basic_tags)

    @staticmethod
    def validate_judgment_result(result: str) -> bool:
        """验证判断结果格式"""
        valid_results = ["新表格", "续表，开始新行", "续表，合并首行"]

        if "判定结果:" not in result:
            return False

        try:
            result_value = result.split("判定结果:")[1].strip()
            return result_value in valid_results
        except (IndexError, AttributeError):
            return False


# 初始化日志系统
LoggingSetup.setup_logging()
